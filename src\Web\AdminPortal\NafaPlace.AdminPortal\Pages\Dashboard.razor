@page "/dashboard"
@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Models.Common
@using NafaPlace.AdminPortal.Models.Products
@using NafaPlace.AdminPortal.Services
@inject IOrderService OrderService
@inject IUserService UserService
@inject ProductService ProductService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Dashboard - NafaPlace Admin</PageTitle>

<link href="~/css/modern-dashboard.css" rel="stylesheet" />

<div class="main-content">
    <!-- Top Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-light">
        <div class="container-fluid">
            <h1 class="navbar-brand mb-0 h1">Dashboard Admin</h1>
            <div class="navbar-nav ms-auto">
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-2"></i>Admin
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/profile"><i class="fas fa-user me-2"></i>Profil</a></li>
                        <li><a class="dropdown-item" href="/settings"><i class="fas fa-cog me-2"></i>Paramètres</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>Déconnexion</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>
        font-size: 0.8rem;
        font-weight: 600;
        color: #27ae60;
    }
    .chart-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 24px;
        margin-bottom: 24px;
    }
    .chart-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #e74c3c;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    .chart-title i {
        margin-right: 8px;
    }
</style>

<div class="container-fluid px-4">
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #e74c3c;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Visiteurs Uniques</p>
                    <h3 class="stats-value">12,5K</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +12,5% vs période précédente
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #27ae60;">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Commandes</p>
                    <h3 class="stats-value">1,2K</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +8,3% vs période précédente
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #f39c12;">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Chiffre d'Affaires</p>
                    <h3 class="stats-value">15,7M GNF</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +16,7% vs période précédente
                    </p>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stats-card d-flex align-items-center">
                <div class="stats-icon" style="background-color: #3498db;">
                    <i class="fas fa-percentage"></i>
                </div>
                <div class="flex-grow-1">
                    <p class="stats-label">Taux de Conversion</p>
                    <h3 class="stats-value">4,2%</h3>
                    <p class="stats-change mb-0">
                        <i class="fas fa-arrow-up"></i> +2,1% vs période précédente
                    </p>
                </div>
            </div>
        </div>
    </div>
    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-danger"></i>
                        Évolution des Ventes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Mois</th>
                                    <th>Ventes</th>
                                    <th>Progression</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Jan</strong></td>
                                    <td>12K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-danger" style="width: 40%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Fév</strong></td>
                                    <td>19K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-warning" style="width: 63%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Mar</strong></td>
                                    <td>15K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-info" style="width: 50%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Avr</strong></td>
                                    <td>25K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" style="width: 83%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Mai</strong></td>
                                    <td>22K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary" style="width: 73%"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Jun</strong></td>
                                    <td>30K GNF</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-dark" style="width: 100%"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4">
            <div class="card">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie text-danger"></i>
                        Top Catégories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="category-list">
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-tshirt text-danger"></i> Mode</span>
                                <strong>35%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-danger" style="width: 35%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-laptop text-warning"></i> Électronique</span>
                                <strong>25%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" style="width: 25%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-home text-success"></i> Maison</span>
                                <strong>20%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-success" style="width: 20%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-football-ball text-info"></i> Sport</span>
                                <strong>15%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-info" style="width: 15%"></div>
                            </div>
                        </div>
                        <div class="category-item mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span><i class="fas fa-book text-secondary"></i> Livres</span>
                                <strong>5%</strong>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-secondary" style="width: 5%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Products Performance Table -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h5 class="chart-title mb-3">Performance des Produits</h5>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Vues</th>
                                <th>Commandes</th>
                                <th>Revenus</th>
                                <th>Taux de Conversion</th>
                                <th>Tendance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.FirstOrDefault())" alt="Sac noir" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Sac noir</strong><br>
                                            <small class="text-muted">ID: 1</small>
                                        </div>
                                    </div>
                                </td>
                                <td>680</td>
                                <td>33</td>
                                <td>347 843 GNF</td>
                                <td>2,5%</td>
                                <td><span class="badge bg-success">↗ +10%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(1).FirstOrDefault())" alt="Voiture" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Voiture</strong><br>
                                            <small class="text-muted">ID: 2</small>
                                        </div>
                                    </div>
                                </td>
                                <td>597</td>
                                <td>12</td>
                                <td>172 459 GNF</td>
                                <td>6,7%</td>
                                <td><span class="badge bg-success">↗ +10%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(2).FirstOrDefault())" alt="Robe de mariage" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Robe de mariage</strong><br>
                                            <small class="text-muted">ID: 3</small>
                                        </div>
                                    </div>
                                </td>
                                <td>608</td>
                                <td>49</td>
                                <td>433 495 GNF</td>
                                <td>0,6%</td>
                                <td><span class="badge bg-success">↗ +18%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(3).FirstOrDefault())" alt="Costume" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Costume</strong><br>
                                            <small class="text-muted">ID: 4</small>
                                        </div>
                                    </div>
                                </td>
                                <td>402</td>
                                <td>11</td>
                                <td>157 356 GNF</td>
                                <td>3,5%</td>
                                <td><span class="badge bg-success">↗ +17%</span></td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <img src="@GetProductImageSrc(_topProducts.Skip(4).FirstOrDefault())" alt="Basket Nike" class="product-img me-3" style="width: 40px; height: 40px; object-fit: cover; border-radius: 8px;">
                                        <div>
                                            <strong>Basket Nike</strong><br>
                                            <small class="text-muted">ID: 5</small>
                                        </div>
                                    </div>
                                </td>
                                <td>583</td>
                                <td>22</td>
                                <td>491 712 GNF</td>
                                <td>1,0%</td>
                                <td><span class="badge bg-success">↗ +27%</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private OrderStatistics? orderStats;
    private int? userCount;
    private bool isLoading = true;
    private List<ProductDto> _topProducts = new List<ProductDto>();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    // Plus besoin d'initialiser les graphiques Chart.js

    private async Task LoadDashboardData()
    {
        isLoading = true;

        try
        {
            // Charger les statistiques des commandes
            var orderStatsTask = OrderService.GetOrderStatisticsAsync();

            // Charger le nombre d'utilisateurs
            var usersTask = UserService.GetUsersAsync(1, 1);

            await Task.WhenAll(orderStatsTask, usersTask);

            orderStats = await orderStatsTask;
            var usersResult = await usersTask;
            userCount = usersResult.TotalCount;

            // Charger les top produits pour l'affichage
            var products = await ProductService.GetProductsAsync();
            _topProducts = products?.Take(5).ToList() ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            // Log error silently - could be enhanced with proper logging service
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    // Plus besoin de flag pour les graphiques

    // Plus besoin d'initialiser Chart.js

    private string GetProductImageSrc(ProductDto? product)
    {
        try
        {
            if (product?.Images != null && product.Images.Any())
            {
                var mainImage = product.Images.FirstOrDefault(i => i.IsMain);
                if (mainImage != null)
                {
                    return ProductService.GetImageUrl(mainImage, true);
                }

                var firstImage = product.Images.FirstOrDefault();
                if (firstImage != null)
                {
                    return ProductService.GetImageUrl(firstImage, true);
                }
            }

            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
        catch (Exception)
        {
            return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=";
        }
    }
}