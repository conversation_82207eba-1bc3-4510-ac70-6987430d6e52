@page "/dashboard-new"
@using NafaPlace.AdminPortal.Models.Orders
@using NafaPlace.AdminPortal.Models.Common
@using NafaPlace.AdminPortal.Models.Products
@using NafaPlace.AdminPortal.Services
@inject IOrderService OrderService
@inject IUserService UserService
@inject ProductService ProductService
@inject IJSRuntime JSRuntime
@attribute [Authorize]

<PageTitle>Dashboard - NafaPlace Admin</PageTitle>

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="~/css/modern-dashboard.css" rel="stylesheet" />

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <div class="sidebar-brand">
                    <h4><i class="fas fa-cube me-2"></i>AdminPro</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">
                            <i class="fas fa-tachometer-alt"></i>
                            Tableau de bord
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/products">
                            <i class="fas fa-box"></i>
                            Produits
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/orders">
                            <i class="fas fa-shopping-cart"></i>
                            Commandes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/users">
                            <i class="fas fa-users"></i>
                            Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/analytics">
                            <i class="fas fa-chart-line"></i>
                            Analytics
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/promotions">
                            <i class="fas fa-tags"></i>
                            Promotions
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/settings">
                            <i class="fas fa-cog"></i>
                            Paramètres
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Tableau de bord</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-sm btn-outline-secondary">Partager</button>
                        <button type="button" class="btn btn-sm btn-outline-secondary">Exporter</button>
                    </div>
                    <button type="button" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>Nouveau produit
                    </button>
                </div>
            </div>

            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card">
                        <div class="card-body d-flex align-items-center">
                            <div class="stats-icon" style="background-color: #8b5cf6;">
                                <i class="fas fa-euro-sign"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="stats-label mb-1">Ventes du jour</p>
                                <h3 class="stats-value mb-1">2 847€</h3>
                                <p class="stats-change mb-0">+12% vs hier</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card success">
                        <div class="card-body d-flex align-items-center">
                            <div class="stats-icon" style="background-color: #06b6d4;">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="stats-label mb-1">Commandes</p>
                                <h3 class="stats-value mb-1">124</h3>
                                <p class="stats-change mb-0">+8% vs hier</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card warning">
                        <div class="card-body d-flex align-items-center">
                            <div class="stats-icon" style="background-color: #f59e0b;">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="stats-label mb-1">Produits</p>
                                <h3 class="stats-value mb-1">1 247</h3>
                                <p class="stats-change mb-0">+2% ce mois</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card stat-card info">
                        <div class="card-body d-flex align-items-center">
                            <div class="stats-icon" style="background-color: #64748b;">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="stats-label mb-1">Clients</p>
                                <h3 class="stats-value mb-1">3 892</h3>
                                <p class="stats-change mb-0">+15% ce mois</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="chart-title mb-0">Évolution des ventes</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="salesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="chart-title mb-0">Top Catégories</h5>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="categoryChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Orders Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Commandes récentes</h5>
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                    Actions
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">Exporter</a></li>
                                    <li><a class="dropdown-item" href="#">Imprimer</a></li>
                                    <li><a class="dropdown-item" href="#">Filtrer</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>N° Commande</th>
                                            <th>Client</th>
                                            <th>Date</th>
                                            <th>Montant</th>
                                            <th>Statut</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>#12547</td>
                                            <td>Marie Martin</td>
                                            <td>29/07/2025</td>
                                            <td>245,50€</td>
                                            <td><span class="badge bg-success">Expédiée</span></td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#">Voir détails</a></li>
                                                        <li><a class="dropdown-item" href="#">Modifier</a></li>
                                                        <li><a class="dropdown-item" href="#">Annuler</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#12546</td>
                                            <td>Pierre Durand</td>
                                            <td>29/07/2025</td>
                                            <td>89,90€</td>
                                            <td><span class="badge bg-warning">En attente</span></td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#">Voir détails</a></li>
                                                        <li><a class="dropdown-item" href="#">Modifier</a></li>
                                                        <li><a class="dropdown-item" href="#">Annuler</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>#12545</td>
                                            <td>Sophie Bernard</td>
                                            <td>28/07/2025</td>
                                            <td>156,75€</td>
                                            <td><span class="badge bg-info">Préparée</span></td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                        <i class="fas fa-ellipsis-v"></i>
                                                    </button>
                                                    <ul class="dropdown-menu">
                                                        <li><a class="dropdown-item" href="#">Voir détails</a></li>
                                                        <li><a class="dropdown-item" href="#">Modifier</a></li>
                                                        <li><a class="dropdown-item" href="#">Annuler</a></li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
    // Sales Chart
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul'],
            datasets: [{
                label: 'Ventes (€)',
                data: [12000, 19000, 15000, 25000, 22000, 30000, 28000],
                borderColor: '#6366f1',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Category Chart
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: ['Électronique', 'Vêtements', 'Maison', 'Sport', 'Livres'],
            datasets: [{
                data: [35, 25, 20, 15, 5],
                backgroundColor: [
                    '#6366f1',
                    '#8b5cf6',
                    '#06b6d4',
                    '#10b981',
                    '#f59e0b'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Mobile sidebar toggle
    document.addEventListener('DOMContentLoaded', function() {
        const sidebarToggle = document.querySelector('.navbar-toggler');
        const sidebar = document.getElementById('sidebarMenu');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth < 768) {
                if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
    });
</script>

@code {
    private OrderStatistics? orderStats;
    private int? userCount;
    private bool isLoading = true;
    private List<ProductDto> _topProducts = new List<ProductDto>();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        isLoading = true;

        try
        {
            // Charger les statistiques des commandes
            var orderStatsTask = OrderService.GetOrderStatisticsAsync();

            // Charger le nombre d'utilisateurs
            var usersTask = UserService.GetUsersAsync(1, 1);

            await Task.WhenAll(orderStatsTask, usersTask);

            orderStats = await orderStatsTask;
            var usersResult = await usersTask;
            userCount = usersResult.TotalCount;

            // Charger les top produits pour l'affichage
            var products = await ProductService.GetProductsAsync();
            _topProducts = products?.Take(5).ToList() ?? new List<ProductDto>();
        }
        catch (Exception ex)
        {
            // Log error silently - could be enhanced with proper logging service
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private string GetProductImageSrc(ProductDto? product)
    {
        if (product?.Images?.Any() == true)
        {
            return product.Images.First().ImageUrl;
        }
        return "/images/placeholder-product.jpg";
    }
}
