@inherits LayoutComponentBase
@inject IAuthService AuthService
@inject NavigationManager NavigationManager
@implements IDisposable
@inject IJSRuntime JSRuntime

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<link href="~/css/modern-dashboard.css" rel="stylesheet" />

<AuthorizeView>
    <Authorized>
        @Body
    </Authorized>
    <NotAuthorized>
        <div class="d-flex justify-content-center align-items-center min-vh-100">
            <div class="text-center">
                <h3>Accès non autorisé</h3>
                <p>Vous devez vous connecter pour accéder à cette page.</p>
                <a href="/login" class="btn btn-primary">Se connecter</a>
            </div>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    protected override void OnInitialized()
    {
        // S'abonner aux changements d'état d'authentification
        AuthService.AuthenticationStateChanged += StateHasChanged;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await JSRuntime.InvokeVoidAsync("reinitializeDropdowns");
    }

    public void Dispose()
    {
        // Se désabonner des changements d'état d'authentification
        AuthService.AuthenticationStateChanged -= StateHasChanged;
    }

    private async Task Logout()
    {
        await AuthService.LogoutAsync();
        NavigationManager.NavigateTo("/login");
    }
}
