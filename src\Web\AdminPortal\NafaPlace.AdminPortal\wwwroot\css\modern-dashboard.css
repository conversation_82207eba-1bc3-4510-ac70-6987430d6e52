/* Modern Dashboard Styles for Admin Portal */

:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --dark-color: #1e293b;
    --light-gray: #f8fafc;
    --border-color: #e2e8f0;
}

body {
    background-color: var(--light-gray);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.page {
    position: relative;
    display: flex;
    flex-direction: row;
    min-height: 100vh;
}

.sidebar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    width: 250px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
}

.sidebar .nav-link {
    color: rgba(255,255,255,0.8);
    padding: 12px 20px;
    margin: 5px 15px;
    border-radius: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: rgba(255,255,255,0.2);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
}

.main-content {
    background-color: var(--light-gray);
    min-height: 100vh;
    margin-left: 250px;
    flex: 1;
}

.navbar {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-bottom: 1px solid var(--border-color);
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
}

.stat-card.success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stat-card.info {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: var(--dark-color);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 15px;
}

.stats-label {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 5px;
}

.stats-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-change {
    font-size: 12px;
    opacity: 0.9;
}

.table-responsive {
    border-radius: 15px;
    overflow: hidden;
}

.table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
}

.badge {
    font-size: 0.75rem;
    padding: 6px 12px;
    border-radius: 20px;
}

.dropdown-menu {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-weight: 600;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
}

.chart-container {
    height: 300px;
    background: white;
    border-radius: 15px;
    padding: 20px;
}

.chart-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 20px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Product image styles */
.product-img {
    width: 40px;
    height: 40px;
    object-fit: cover;
    border-radius: 8px;
}

/* Sidebar brand */
.sidebar-brand {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.sidebar-brand h4 {
    color: white;
    margin: 0;
    font-weight: 600;
}

/* Navigation improvements */
.nav-pills .nav-link {
    border-radius: 10px;
    margin: 2px 0;
}

.nav-pills .nav-link.active {
    background-color: rgba(255,255,255,0.2);
}

/* Card header improvements */
.card-header {
    background: transparent;
    border-bottom: 1px solid var(--border-color);
    padding: 20px;
}

.card-body {
    padding: 20px;
}

/* Button improvements */
.btn-outline-secondary {
    border-color: var(--border-color);
    color: var(--dark-color);
}

.btn-outline-secondary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Table improvements */
.table-hover tbody tr:hover {
    background-color: rgba(99, 102, 241, 0.05);
}

/* Dropdown improvements */
.dropdown-toggle::after {
    margin-left: 8px;
}

/* Alert improvements */
.alert {
    border-radius: 10px;
    border: none;
}
